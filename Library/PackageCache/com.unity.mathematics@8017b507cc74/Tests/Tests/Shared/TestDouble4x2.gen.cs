//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. To update the generation of this file, modify and re-run Unity.Mathematics.CodeGen.
// </auto-generated>
//------------------------------------------------------------------------------
using NUnit.Framework;
using static Unity.Mathematics.math;
using Burst.Compiler.IL.Tests;

namespace Unity.Mathematics.Tests
{
    [TestFixture]
    public class TestDouble4x2
    {
        [TestCompiler]
        public static void double4x2_zero()
        {
            TestUtils.AreEqual(0.0, double4x2.zero.c0.x);
            TestUtils.AreEqual(0.0, double4x2.zero.c0.y);
            TestUtils.AreEqual(0.0, double4x2.zero.c0.z);
            TestUtils.AreEqual(0.0, double4x2.zero.c0.w);
            TestUtils.AreEqual(0.0, double4x2.zero.c1.x);
            TestUtils.AreEqual(0.0, double4x2.zero.c1.y);
            TestUtils.AreEqual(0.0, double4x2.zero.c1.z);
            TestUtils.AreEqual(0.0, double4x2.zero.c1.w);
        }

        [TestCompiler]
        public static void double4x2_operator_equal_wide_wide()
        {
            double4x2 a0 = double4x2(492.15758275061728, -495.20632027797694, 227.45765195947968, -147.37405950733182, -222.68201909897942, 64.093720704360749, -23.890404473939157, -16.8197190839889);
            double4x2 b0 = double4x2(192.56880888369346, -235.61102472786376, -254.04311740307281, -412.62472052715009, 471.90480945627428, -6.4727852374654162, -339.10237447316865, 488.1875700839737);
            bool4x2 r0 = bool4x2(false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r0, a0 == b0);

            double4x2 a1 = double4x2(163.23210890741655, -165.27101071424363, 470.87767980568003, -423.94255967808078, 109.63436918595539, 462.69031283943468, -335.38147727371262, 357.23446934168896);
            double4x2 b1 = double4x2(-379.5965842584132, -308.41700258311675, -82.333374300195544, -102.92108087563935, 226.51573835430463, -356.90132896830391, -362.91277544708589, -427.89843746083716);
            bool4x2 r1 = bool4x2(false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r1, a1 == b1);

            double4x2 a2 = double4x2(1.5455777652308598, -347.38824741327585, -114.47217302884542, 435.84865804940864, 194.23808607563285, 138.76554710174241, -467.34914205379278, 370.43337767684523);
            double4x2 b2 = double4x2(466.65013978753711, -102.79904680270658, -43.355954428834821, 85.045664111639212, -91.127054972167628, 422.19208856215334, -477.43130873024057, 1.8770024785198984);
            bool4x2 r2 = bool4x2(false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r2, a2 == b2);

            double4x2 a3 = double4x2(476.70826147343416, 320.55264702465047, -498.59197377534207, 92.4169581366782, 104.51136856177425, 166.75460608618084, -204.73343024250744, 434.75675674656259);
            double4x2 b3 = double4x2(312.5800799394865, 254.59934365684137, 352.72583763335172, 62.490957050812881, 119.71476059766246, -511.05808639482507, -302.47273053902791, -371.76924365189359);
            bool4x2 r3 = bool4x2(false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r3, a3 == b3);
        }

        [TestCompiler]
        public static void double4x2_operator_equal_wide_scalar()
        {
            double4x2 a0 = double4x2(-303.2300766926399, 451.52631327674089, -253.65587413201848, -105.20363502632995, -500.6910920090466, -426.19248338518315, 159.87609656149334, -59.558379439431405);
            double b0 = (123.5445759871717);
            bool4x2 r0 = bool4x2(false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r0, a0 == b0);

            double4x2 a1 = double4x2(-57.477391031327386, 406.51375861024189, 370.88599866017978, -172.03530629539642, 455.40001198993991, -11.338988547836891, 363.93823044557973, -27.150561106927);
            double b1 = (-182.04973968400139);
            bool4x2 r1 = bool4x2(false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r1, a1 == b1);

            double4x2 a2 = double4x2(-325.97606507221985, 180.19686635779067, -374.12832015293105, -439.35894295170851, -126.54608899287234, -197.2617896521752, -227.15933357326281, -479.8991937487848);
            double b2 = (-290.35904254129116);
            bool4x2 r2 = bool4x2(false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r2, a2 == b2);

            double4x2 a3 = double4x2(-439.77767750237962, -224.51705013239621, -422.83322616239695, -450.19627043707123, -20.106708774392814, 297.37999906082632, 185.9665759475746, -102.97598962810633);
            double b3 = (-495.23734902555);
            bool4x2 r3 = bool4x2(false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r3, a3 == b3);
        }

        [TestCompiler]
        public static void double4x2_operator_equal_scalar_wide()
        {
            double a0 = (-253.39728534100453);
            double4x2 b0 = double4x2(19.952187785856495, -185.79199346610903, 407.8136052600172, -87.2766969610363, -206.27469382354741, 160.503138855334, -274.77081478516141, -2.6315281403397535);
            bool4x2 r0 = bool4x2(false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r0, a0 == b0);

            double a1 = (448.35453602688131);
            double4x2 b1 = double4x2(-410.03524251004461, 247.32901465489022, 355.53915350303942, -298.06671180299793, 414.10151429385951, -481.30262707234482, 196.55074438664633, 34.60100008668428);
            bool4x2 r1 = bool4x2(false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r1, a1 == b1);

            double a2 = (113.76156645350227);
            double4x2 b2 = double4x2(-386.45337861890596, -124.49174672201821, 243.8866447153905, -492.6181826501238, 145.424413033493, 421.55070968230757, -95.409988209330493, 336.80928746648567);
            bool4x2 r2 = bool4x2(false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r2, a2 == b2);

            double a3 = (209.58380589707929);
            double4x2 b3 = double4x2(487.441424358376, 161.80653365040507, 149.84247095409899, 225.723996505944, -71.21880176999548, 85.780251781353854, 192.547256797807, -49.887493395194156);
            bool4x2 r3 = bool4x2(false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r3, a3 == b3);
        }

        [TestCompiler]
        public static void double4x2_operator_not_equal_wide_wide()
        {
            double4x2 a0 = double4x2(430.8425316432689, 104.69001798736394, 225.80243478799355, -310.57017841496048, -418.61945815506363, 304.12820281839379, -509.32682561749908, -160.53807719076895);
            double4x2 b0 = double4x2(210.02470622305975, -55.203330304102678, -269.92533672504373, -234.54673372700194, 25.917412054686565, -63.726991444699024, -484.55371092471933, -425.333599050219);
            bool4x2 r0 = bool4x2(true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r0, a0 != b0);

            double4x2 a1 = double4x2(-203.30197606016975, -505.76325368590807, 162.17220623892365, 1.1561973100324394, 65.662074358045174, 102.78780250567377, 172.93008120960098, 26.621009123800832);
            double4x2 b1 = double4x2(-53.274394775402925, 328.1944192984115, 15.963139303011417, 461.71412417931208, -113.36304455313973, -240.07297264787974, 495.11916970420589, 203.5583661550462);
            bool4x2 r1 = bool4x2(true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r1, a1 != b1);

            double4x2 a2 = double4x2(235.12595259171258, 128.54198533321824, -354.99697630246959, 334.35948220564023, -495.83200692377613, 468.30740163675853, 458.37094733601941, 299.93733300824522);
            double4x2 b2 = double4x2(340.49345103860526, -241.90719448863865, 459.56982896270688, 213.0737384357833, -384.7828506831, -255.07233846144396, 477.66343115161328, -248.03662621604121);
            bool4x2 r2 = bool4x2(true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r2, a2 != b2);

            double4x2 a3 = double4x2(43.12718560319729, -354.71349994964595, -145.28719551176169, 390.80186218340032, -303.13149108697263, 391.13459533785215, 139.2868607692825, 104.52318506339714);
            double4x2 b3 = double4x2(-407.92344565313471, -199.78886971240343, 151.84326488889906, -97.120607659742518, 154.97589380805186, -172.83452065886672, 441.5027942329192, -401.73862785926957);
            bool4x2 r3 = bool4x2(true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r3, a3 != b3);
        }

        [TestCompiler]
        public static void double4x2_operator_not_equal_wide_scalar()
        {
            double4x2 a0 = double4x2(-16.914588697680529, 168.83411486858233, -462.71352145760949, 130.30776959765137, 214.50161443208424, -440.26328178879959, -197.12796053529155, -169.09985860115842);
            double b0 = (-145.37277109239847);
            bool4x2 r0 = bool4x2(true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r0, a0 != b0);

            double4x2 a1 = double4x2(-386.61117595555783, -270.26885593601912, -403.96372313236992, -269.80570877241234, 299.65422763473089, -71.750904831919286, -432.75573917513515, -457.36312100727258);
            double b1 = (-281.02101362916687);
            bool4x2 r1 = bool4x2(true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r1, a1 != b1);

            double4x2 a2 = double4x2(-13.519590622521719, 185.042454567292, -482.53069351731364, 116.39514427836764, 511.73495578753523, 230.50753628020527, 100.27476768394683, 129.68240863163135);
            double b2 = (273.87305773136814);
            bool4x2 r2 = bool4x2(true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r2, a2 != b2);

            double4x2 a3 = double4x2(321.17879048044733, 140.33521921016984, 369.2123617461009, 453.81121489676241, -333.66624871532724, -373.93775218256644, 150.20429451307484, -442.16476627912596);
            double b3 = (-220.63900409482375);
            bool4x2 r3 = bool4x2(true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r3, a3 != b3);
        }

        [TestCompiler]
        public static void double4x2_operator_not_equal_scalar_wide()
        {
            double a0 = (275.79582823244664);
            double4x2 b0 = double4x2(-57.196896341255353, -382.4325279586169, 97.820359990848374, -161.46364529499022, -458.39563367254829, -499.61786364932448, 327.92217818271467, 367.57121699283425);
            bool4x2 r0 = bool4x2(true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r0, a0 != b0);

            double a1 = (59.7863667289663);
            double4x2 b1 = double4x2(-209.58068118318016, -62.580453186566217, -479.97497604786184, -49.494519495169868, -114.68521338081229, 109.93924599044919, -176.28482755286842, -347.48529903380449);
            bool4x2 r1 = bool4x2(true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r1, a1 != b1);

            double a2 = (85.540928165214609);
            double4x2 b2 = double4x2(-356.65954868712441, -104.24357490625397, -133.54918605347592, 243.53971135036079, 13.141311890045813, -379.98594754747393, -41.281226892620907, 87.911684792447659);
            bool4x2 r2 = bool4x2(true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r2, a2 != b2);

            double a3 = (-339.07727996403224);
            double4x2 b3 = double4x2(-371.82034533648766, 333.14425936953364, 294.81196011920088, -187.14565977228136, 220.19225774528093, -228.18207250730234, -499.72373914146971, 97.4059055305114);
            bool4x2 r3 = bool4x2(true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r3, a3 != b3);
        }

        [TestCompiler]
        public static void double4x2_operator_less_wide_wide()
        {
            double4x2 a0 = double4x2(196.84256825076534, 336.40979997087732, 251.96372115424072, 257.65591466503963, 430.04588647840819, -62.419644146421774, 8.8392293494376872, -333.81671563434259);
            double4x2 b0 = double4x2(-465.34502313348696, -256.15239751346053, -314.814018634527, 364.56673662949663, 100.21050290959442, 182.56098636545289, 3.116978885194726, -259.43047893207074);
            bool4x2 r0 = bool4x2(false, false, false, true, false, true, false, true);
            TestUtils.AreEqual(r0, a0 < b0);

            double4x2 a1 = double4x2(164.67880662003472, -350.94487516532877, 3.84143662631584, 125.40972024081725, -111.12994127680076, 70.005523475820951, 448.19828173527412, -419.98711200244122);
            double4x2 b1 = double4x2(-437.33490749696966, -456.0437321402336, -394.2559718537405, 401.91369099259077, 313.43916454605721, 121.28668194696616, -28.012290729215522, -282.96589697663012);
            bool4x2 r1 = bool4x2(false, false, false, true, true, true, false, true);
            TestUtils.AreEqual(r1, a1 < b1);

            double4x2 a2 = double4x2(-258.30166757213965, -34.832201735504043, -69.859397682295821, 67.767227442826766, -139.77729207825723, 385.43464130229995, 133.707390609061, 506.18837117878184);
            double4x2 b2 = double4x2(330.06440631023816, 124.09937077579059, -183.69031700104955, 373.0607623406969, 109.75094013556418, -203.57134232463841, 45.6486556742567, -360.95226280808089);
            bool4x2 r2 = bool4x2(true, true, false, true, true, false, false, false);
            TestUtils.AreEqual(r2, a2 < b2);

            double4x2 a3 = double4x2(34.442885653322037, 412.11373896715872, -84.809773246203463, 444.78534504621541, -78.754743374304269, 366.97754376334024, 127.18045788965208, 428.36845489422251);
            double4x2 b3 = double4x2(211.91309867236441, -313.28636207863985, -259.66108691862837, 79.0985401045059, 446.49610897828643, 450.52455660818362, -375.63076728192658, -53.941822792376286);
            bool4x2 r3 = bool4x2(true, false, false, false, true, true, false, false);
            TestUtils.AreEqual(r3, a3 < b3);
        }

        [TestCompiler]
        public static void double4x2_operator_less_wide_scalar()
        {
            double4x2 a0 = double4x2(-132.05731708000292, -192.46500477216438, -66.834607870706634, -379.01750081545561, -360.28242199508588, 20.927834282129879, -158.24074537970159, 437.34587522845061);
            double b0 = (-156.01021845452965);
            bool4x2 r0 = bool4x2(false, true, false, true, true, false, true, false);
            TestUtils.AreEqual(r0, a0 < b0);

            double4x2 a1 = double4x2(-20.452607402788772, 307.48418607725023, 274.01523292903562, 373.54965584983563, 398.52368301829495, 105.0301654827922, -58.010895994496934, 109.67008810381878);
            double b1 = (225.29148517609178);
            bool4x2 r1 = bool4x2(true, false, false, false, false, true, true, true);
            TestUtils.AreEqual(r1, a1 < b1);

            double4x2 a2 = double4x2(-108.853174498702, 140.42607147080173, -500.08827638071415, 172.10334857371788, -197.50074610370245, -7.27149987559369, -432.99049898283113, 62.158315449095426);
            double b2 = (-44.971252223929014);
            bool4x2 r2 = bool4x2(true, false, true, false, true, false, true, false);
            TestUtils.AreEqual(r2, a2 < b2);

            double4x2 a3 = double4x2(-72.254720959931035, -500.25573586870718, 149.1149638393498, 119.88061695912882, 202.63918909925928, 274.95066393304182, 66.4120323967245, 274.99944580486022);
            double b3 = (-377.85232299279994);
            bool4x2 r3 = bool4x2(false, true, false, false, false, false, false, false);
            TestUtils.AreEqual(r3, a3 < b3);
        }

        [TestCompiler]
        public static void double4x2_operator_less_scalar_wide()
        {
            double a0 = (-423.117411095238);
            double4x2 b0 = double4x2(385.09483617595151, -123.93348532725753, 86.376572887588509, 133.44217378154497, 161.45794947513286, 229.75426660746064, 222.57159934871436, 315.53116360098647);
            bool4x2 r0 = bool4x2(true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r0, a0 < b0);

            double a1 = (-447.20351883731945);
            double4x2 b1 = double4x2(271.83385790131695, -393.60531324595462, 317.48689737798964, -164.6051085761772, -282.87605370342544, 296.97953071118309, -254.40115582868509, 365.61562054493265);
            bool4x2 r1 = bool4x2(true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r1, a1 < b1);

            double a2 = (-441.98425671178114);
            double4x2 b2 = double4x2(-131.42866021554391, 442.62897631275882, -29.792842163607872, -138.37379533535511, 9.2169721169476588, -226.7305482489665, 171.02944310523083, 376.62522595777421);
            bool4x2 r2 = bool4x2(true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r2, a2 < b2);

            double a3 = (-462.58872697436658);
            double4x2 b3 = double4x2(-142.36729795409707, -456.25377414014832, 66.6102416825529, 169.37875779409831, 327.44439450253003, 64.0879266560487, -153.50390369887646, 199.38014921889646);
            bool4x2 r3 = bool4x2(true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r3, a3 < b3);
        }

        [TestCompiler]
        public static void double4x2_operator_greater_wide_wide()
        {
            double4x2 a0 = double4x2(483.50140141113729, 310.81563415695712, 106.9661896726891, 295.73526038589671, 116.95757179938141, -478.29977653841479, -14.897393471979228, -33.817441717636484);
            double4x2 b0 = double4x2(-471.39802454011425, -371.98528617060992, 36.900723236101044, -316.76360407320954, 19.683055648432628, 207.3091381561519, 362.79748861994483, 324.95341816775192);
            bool4x2 r0 = bool4x2(true, true, true, true, true, false, false, false);
            TestUtils.AreEqual(r0, a0 > b0);

            double4x2 a1 = double4x2(-24.740548383789417, 319.78262701620474, -120.15856581561201, -289.00857962714906, 455.85146662958505, 144.70691139283917, 63.931990891663304, -285.68304099034663);
            double4x2 b1 = double4x2(340.94807140014507, 25.986035120666997, -114.2111352021858, 240.80346428640348, 273.42244757033063, 325.51576224226312, 27.341068995809678, 64.479532510265472);
            bool4x2 r1 = bool4x2(false, true, false, false, true, false, true, false);
            TestUtils.AreEqual(r1, a1 > b1);

            double4x2 a2 = double4x2(-502.0907201720824, -337.19446412529538, 474.31734274063137, -507.14510679018923, -133.56559735795742, -443.10913654934109, -464.34137056038776, -68.361549647693323);
            double4x2 b2 = double4x2(200.94836983501375, 100.12266998184964, -79.00710896356361, -315.137945560337, -122.98542815213347, -163.77920229908972, -492.56600617457462, -90.797273439726439);
            bool4x2 r2 = bool4x2(false, false, true, false, false, false, true, true);
            TestUtils.AreEqual(r2, a2 > b2);

            double4x2 a3 = double4x2(-185.99299987870876, -157.80389340119615, -74.124229227250567, -94.471165939453613, 329.61055508703487, -315.83675280019486, 404.193811843262, 131.30440503512716);
            double4x2 b3 = double4x2(-284.9012335673446, -23.653687249707843, 174.93002112905026, 85.7125366133231, -441.98783012944637, 345.54374210235835, 482.21949814363359, -422.38349719642827);
            bool4x2 r3 = bool4x2(true, false, false, false, true, false, false, true);
            TestUtils.AreEqual(r3, a3 > b3);
        }

        [TestCompiler]
        public static void double4x2_operator_greater_wide_scalar()
        {
            double4x2 a0 = double4x2(64.317918092160426, -397.70346445483318, 431.87690826499693, 85.702980796668157, 246.26305233978803, 197.49155602114809, 286.1994608781298, 280.81334818564972);
            double b0 = (305.85991992888034);
            bool4x2 r0 = bool4x2(false, false, true, false, false, false, false, false);
            TestUtils.AreEqual(r0, a0 > b0);

            double4x2 a1 = double4x2(-405.78459210218148, -241.80727326209063, 333.57817498481745, 370.27919524269146, -413.70138116073861, -356.5923551789449, -353.03129522550444, 396.64532608382649);
            double b1 = (171.56538661362856);
            bool4x2 r1 = bool4x2(false, false, true, true, false, false, false, true);
            TestUtils.AreEqual(r1, a1 > b1);

            double4x2 a2 = double4x2(467.22205541432936, 502.91505193287276, 315.46759024051369, -259.28970134411458, 281.23064554912537, 428.79219909608, 245.15306460352292, -279.17542494422543);
            double b2 = (-240.0134228393498);
            bool4x2 r2 = bool4x2(true, true, true, false, true, true, true, false);
            TestUtils.AreEqual(r2, a2 > b2);

            double4x2 a3 = double4x2(-453.86309668694764, -425.65293451103054, 99.132852838902181, 355.0605339273161, -456.43941256796916, 154.48902208846482, 405.52974409867534, -157.73379643155903);
            double b3 = (-124.77154856769909);
            bool4x2 r3 = bool4x2(false, false, true, true, false, true, true, false);
            TestUtils.AreEqual(r3, a3 > b3);
        }

        [TestCompiler]
        public static void double4x2_operator_greater_scalar_wide()
        {
            double a0 = (-282.67049635698572);
            double4x2 b0 = double4x2(358.09997360692353, -72.5964134077525, -232.16380106292843, -60.706723956720282, 75.156642710397364, 150.88350040786133, 339.53917924479538, -498.19602965665797);
            bool4x2 r0 = bool4x2(false, false, false, false, false, false, false, true);
            TestUtils.AreEqual(r0, a0 > b0);

            double a1 = (459.74610326241054);
            double4x2 b1 = double4x2(-227.96872316485678, 335.86213485145106, 76.178844248959308, 296.85993899817572, 177.49000390688423, -281.20120657663847, 244.72285162877427, 137.32857257562159);
            bool4x2 r1 = bool4x2(true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r1, a1 > b1);

            double a2 = (-385.33824724021287);
            double4x2 b2 = double4x2(443.16345879210326, -353.56254141105455, 26.040673983302327, -331.7939499969566, -43.691963454565041, 20.949428806523542, -211.17984423934473, 227.42171894173214);
            bool4x2 r2 = bool4x2(false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r2, a2 > b2);

            double a3 = (-84.7797711290325);
            double4x2 b3 = double4x2(-375.13548701588786, -205.17813096064054, -197.04714617368165, -219.63402305340117, -210.01563344244641, -266.773715858708, 144.77848703450456, -471.71120069535039);
            bool4x2 r3 = bool4x2(true, true, true, true, true, true, false, true);
            TestUtils.AreEqual(r3, a3 > b3);
        }

        [TestCompiler]
        public static void double4x2_operator_less_equal_wide_wide()
        {
            double4x2 a0 = double4x2(-438.52313753521219, 210.48942837980087, 4.8773329280677444, -137.29793817237857, 156.09410174009111, -363.92412035722475, -97.948485181642923, 437.29539009430232);
            double4x2 b0 = double4x2(-474.8141498392514, 304.3710555063426, 234.8241737982371, -390.48543209139513, -297.17535295019638, -326.29239121372461, 107.2538764976216, -413.13107342884462);
            bool4x2 r0 = bool4x2(false, true, true, false, false, true, true, false);
            TestUtils.AreEqual(r0, a0 <= b0);

            double4x2 a1 = double4x2(458.53029153241323, -294.06474675520542, 23.622613679441884, -34.284056441059363, 149.736484835733, -418.8866781754823, -197.50252899783783, -88.2055118494693);
            double4x2 b1 = double4x2(67.094432623635271, 470.07522724106684, -84.499104777583455, 392.78422683886447, -263.53175485484849, 369.30090039284005, -333.32529298091555, 238.41347443238533);
            bool4x2 r1 = bool4x2(false, true, false, true, false, true, false, true);
            TestUtils.AreEqual(r1, a1 <= b1);

            double4x2 a2 = double4x2(-376.71814292330208, 341.62712899857536, -83.309179106405566, -107.49073295830317, 319.46688833807912, 205.35738501574724, 345.56372968552807, 395.32190746596177);
            double4x2 b2 = double4x2(486.24259279959028, 279.65021408705513, 236.05201803709008, 132.75898248178839, 66.294708998079727, 183.00210699020056, 200.13055071613314, 339.043800750302);
            bool4x2 r2 = bool4x2(true, false, true, true, false, false, false, false);
            TestUtils.AreEqual(r2, a2 <= b2);

            double4x2 a3 = double4x2(-222.87415490992095, 439.02200790821666, -368.0755667016262, -200.03860173003682, 71.46990660180802, -357.36542932939039, 141.7108519737194, 319.0170969064427);
            double4x2 b3 = double4x2(438.53791710293751, 145.40187866306019, 178.16310199450845, 157.97596724237133, 329.7052015409364, -243.59091221708383, 5.4011614347813293, -22.580605278993289);
            bool4x2 r3 = bool4x2(true, false, true, true, true, true, false, false);
            TestUtils.AreEqual(r3, a3 <= b3);
        }

        [TestCompiler]
        public static void double4x2_operator_less_equal_wide_scalar()
        {
            double4x2 a0 = double4x2(193.4958237118534, 168.91555197952107, -313.9930695565385, 81.826965131716292, 18.503590830836288, -0.35819602029312136, 241.36115776810846, -463.81641242644582);
            double b0 = (443.85054299042122);
            bool4x2 r0 = bool4x2(true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r0, a0 <= b0);

            double4x2 a1 = double4x2(-1.3577692515020203, 398.9919504593089, -471.253072242836, -264.93778264938749, 82.258299150624453, 11.246050124636895, 424.7040156911612, 426.48223157715154);
            double b1 = (-268.89945591096739);
            bool4x2 r1 = bool4x2(false, false, true, false, false, false, false, false);
            TestUtils.AreEqual(r1, a1 <= b1);

            double4x2 a2 = double4x2(56.319978501796754, 31.901173844887467, -152.2575724833913, -437.92645975478297, -37.104814785115821, -47.144214413661587, 333.6230348710078, -274.80387438219225);
            double b2 = (-196.28791126808522);
            bool4x2 r2 = bool4x2(false, false, false, true, false, false, false, true);
            TestUtils.AreEqual(r2, a2 <= b2);

            double4x2 a3 = double4x2(358.67627804292192, 192.30916008367626, 145.30606777281787, -466.13296363602063, -494.26732968458316, -111.57013922164691, -139.54120332540072, -146.58935148389514);
            double b3 = (-260.46056926458169);
            bool4x2 r3 = bool4x2(false, false, false, true, true, false, false, false);
            TestUtils.AreEqual(r3, a3 <= b3);
        }

        [TestCompiler]
        public static void double4x2_operator_less_equal_scalar_wide()
        {
            double a0 = (393.60626644343427);
            double4x2 b0 = double4x2(-75.688363825757222, -44.2638714519627, 125.86491566797019, 191.96488174794467, 13.543054825413492, -197.0519259893577, -423.945100743298, -330.04861680141119);
            bool4x2 r0 = bool4x2(false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r0, a0 <= b0);

            double a1 = (420.16553779140372);
            double4x2 b1 = double4x2(105.54730777887039, 174.82126363311954, 296.71757831085358, -469.70041845259277, 123.26718979853536, 112.9969695140594, 495.14339493920249, -488.65789364681478);
            bool4x2 r1 = bool4x2(false, false, false, false, false, false, true, false);
            TestUtils.AreEqual(r1, a1 <= b1);

            double a2 = (388.53941148730894);
            double4x2 b2 = double4x2(-493.24077080806751, 16.451064832718657, -387.6516336815672, -229.1773127192526, -373.01533930982248, -391.142134610164, 90.994149488859875, -178.39613517485378);
            bool4x2 r2 = bool4x2(false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r2, a2 <= b2);

            double a3 = (-69.621067317957568);
            double4x2 b3 = double4x2(471.7908458522478, -67.4667532758167, 45.305359623071467, -154.69219000390365, 385.73888248286153, -431.652945004242, -331.67304841227508, -349.89271013340573);
            bool4x2 r3 = bool4x2(true, true, true, false, true, false, false, false);
            TestUtils.AreEqual(r3, a3 <= b3);
        }

        [TestCompiler]
        public static void double4x2_operator_greater_equal_wide_wide()
        {
            double4x2 a0 = double4x2(-507.92858409692, 504.49748181947393, -385.43449205226938, -262.32340944107784, -37.550928848586466, -111.59527759980193, -463.70202157632542, 387.44885772627265);
            double4x2 b0 = double4x2(-81.346509732933043, 297.66615047010885, 171.06540616371922, -431.03805538222105, -6.859075311040101, 319.72570362674333, 254.079170106947, 396.5724000393285);
            bool4x2 r0 = bool4x2(false, true, false, true, false, false, false, false);
            TestUtils.AreEqual(r0, a0 >= b0);

            double4x2 a1 = double4x2(456.96878573716094, -211.01015506079892, 182.41135391146474, -53.596053863687473, -309.57021608463032, -136.02249127999994, 280.73629082401112, -96.9958942388165);
            double4x2 b1 = double4x2(178.83927615864172, -447.06336304501787, 288.49268569075161, 474.88929460704765, -321.75022831640683, -395.97722048125104, -158.69246037243516, 391.48869318118727);
            bool4x2 r1 = bool4x2(true, true, false, false, true, true, true, false);
            TestUtils.AreEqual(r1, a1 >= b1);

            double4x2 a2 = double4x2(-174.05950673579213, 88.9019382413951, 43.816040774721728, -446.07842585354967, 16.645595796706857, 409.83252043734888, -191.32987245886113, 222.99782548798146);
            double4x2 b2 = double4x2(-368.10924141859135, 89.1238043723273, -510.27932214656812, -486.92979525352354, -81.215552606254619, 274.21882046117389, -212.88155494112596, 288.99530591117);
            bool4x2 r2 = bool4x2(true, false, true, true, true, true, true, false);
            TestUtils.AreEqual(r2, a2 >= b2);

            double4x2 a3 = double4x2(404.28838915577546, 230.60328136691976, -441.78928228923553, -86.293056289801882, 484.24954413075443, 95.2363665547391, -204.91210255628084, -199.77434620623211);
            double4x2 b3 = double4x2(307.73173131967508, 307.24516620638087, -199.39178213821339, -284.42126978767163, -482.39181278757371, 448.3157362641374, -378.3461889598268, -390.8584684761513);
            bool4x2 r3 = bool4x2(true, false, false, true, true, false, true, true);
            TestUtils.AreEqual(r3, a3 >= b3);
        }

        [TestCompiler]
        public static void double4x2_operator_greater_equal_wide_scalar()
        {
            double4x2 a0 = double4x2(465.15218732559686, -424.8860745024337, -209.22109685150025, 58.779852656079356, -302.26910533675414, 140.12558252183976, 16.353385694489475, -344.55997316192838);
            double b0 = (-5.5998842742293391);
            bool4x2 r0 = bool4x2(true, false, false, true, false, true, true, false);
            TestUtils.AreEqual(r0, a0 >= b0);

            double4x2 a1 = double4x2(393.27804846003562, 441.0115565923096, -509.78156757251435, -36.994287269652943, 494.82028865014217, -164.97393830352183, -466.12009046325466, -123.8137477020797);
            double b1 = (-315.70155086913218);
            bool4x2 r1 = bool4x2(true, true, false, true, true, true, false, true);
            TestUtils.AreEqual(r1, a1 >= b1);

            double4x2 a2 = double4x2(215.65121779947128, 314.34603014325069, 190.51609882643265, -83.111429014760745, -23.836435567511444, 143.04935962662535, -264.91997945724052, -169.70222457205051);
            double b2 = (104.99569730879534);
            bool4x2 r2 = bool4x2(true, true, true, false, false, true, false, false);
            TestUtils.AreEqual(r2, a2 >= b2);

            double4x2 a3 = double4x2(329.70751610850334, -260.42331016269668, 354.19514219565087, -111.84533768140028, 33.309096113456917, 355.63126938214123, -435.36056753404466, -38.39930893778768);
            double b3 = (359.09582035573931);
            bool4x2 r3 = bool4x2(false, false, false, false, false, false, false, false);
            TestUtils.AreEqual(r3, a3 >= b3);
        }

        [TestCompiler]
        public static void double4x2_operator_greater_equal_scalar_wide()
        {
            double a0 = (374.82703393270594);
            double4x2 b0 = double4x2(-1.609757185731894, 338.61524049314448, -116.18140392945213, -332.15732375353451, -355.9793509710484, -468.90144107719021, 38.579884785497484, -332.34754697063357);
            bool4x2 r0 = bool4x2(true, true, true, true, true, true, true, true);
            TestUtils.AreEqual(r0, a0 >= b0);

            double a1 = (2.8901150240051265);
            double4x2 b1 = double4x2(467.77776477661814, 121.40638762405445, -305.02337303060267, -58.428812292604164, -226.51955209789776, -47.020994446715804, 305.3026770582901, -427.40123315686418);
            bool4x2 r1 = bool4x2(false, false, true, true, true, true, false, true);
            TestUtils.AreEqual(r1, a1 >= b1);

            double a2 = (92.263649745035764);
            double4x2 b2 = double4x2(-497.17853736187266, -408.62564225151465, -455.23049113491106, 396.42608637196292, -469.29488561548987, -485.7540130493017, -182.34619268325446, -291.54536284671417);
            bool4x2 r2 = bool4x2(true, true, true, false, true, true, true, true);
            TestUtils.AreEqual(r2, a2 >= b2);

            double a3 = (278.740809331993);
            double4x2 b3 = double4x2(-75.87113932327884, 28.907059921374071, 287.72014988945807, 420.50978990109161, 473.62684152723614, 181.514540518408, -369.20287220981106, 243.74977385427326);
            bool4x2 r3 = bool4x2(true, true, false, false, false, true, true, true);
            TestUtils.AreEqual(r3, a3 >= b3);
        }

        [TestCompiler]
        public static void double4x2_operator_add_wide_wide()
        {
            double4x2 a0 = double4x2(506.12905263627374, -501.77980803967444, 420.08479638587903, -186.03206476291274, -9.3123953385801883, 328.51179686585056, 424.34407659263536, 87.791079800478656);
            double4x2 b0 = double4x2(-28.757987751047096, -337.135153689019, -340.676816860529, 152.31202633320913, 423.66745420157326, 90.374096674087468, 376.18866246574964, 1.7671887882831925);
            double4x2 r0 = double4x2(477.37106488522664, -838.91496172869347, 79.407979525350015, -33.720038429703607, 414.35505886299308, 418.885893539938, 800.532739058385, 89.558268588761848);
            TestUtils.AreEqual(r0, a0 + b0);

            double4x2 a1 = double4x2(462.41368148402012, -46.178705952213477, 401.17006296718966, -454.12414643453627, 69.195687564646732, -177.95734485329939, 299.60415544156183, 340.7048587001417);
            double4x2 b1 = double4x2(-120.18586045139745, -279.62936628965167, -344.66710273580026, 242.8391956029642, 418.5930504363929, -23.312797318823982, -95.099945827899489, 147.92812568877275);
            double4x2 r1 = double4x2(342.22782103262267, -325.80807224186515, 56.502960231389409, -211.28495083157208, 487.78873800103963, -201.27014217212337, 204.50420961366234, 488.63298438891445);
            TestUtils.AreEqual(r1, a1 + b1);

            double4x2 a2 = double4x2(219.91602740991675, -321.90838232725321, 286.35534037573041, -333.41949311523672, -118.93216973120911, 68.607509406566351, 23.190902005504313, -205.57787547147734);
            double4x2 b2 = double4x2(331.03287926830023, -82.502564230236487, 279.44956291813844, 342.6227215931857, -300.35853185335105, -209.69408736456842, 446.55942150883345, -351.98918955027557);
            double4x2 r2 = double4x2(550.948906678217, -404.41094655748969, 565.80490329386885, 9.2032284779489828, -419.29070158456017, -141.08657795800207, 469.75032351433777, -557.56706502175291);
            TestUtils.AreEqual(r2, a2 + b2);

            double4x2 a3 = double4x2(11.521422629953122, -340.7950796283759, -68.931167873056211, 304.8532370556394, -86.633841316510825, 105.66915874633435, 349.28052799277032, 364.7078708916473);
            double4x2 b3 = double4x2(-263.12385642860261, -252.4585670216282, 289.82535542632706, 338.79615537207394, -232.61900364263869, -510.50825405051387, 349.2807325559113, -426.2124495106807);
            double4x2 r3 = double4x2(-251.60243379864949, -593.25364665000416, 220.89418755327085, 643.64939242771334, -319.25284495914951, -404.83909530417952, 698.56126054868162, -61.504578619033396);
            TestUtils.AreEqual(r3, a3 + b3);
        }

        [TestCompiler]
        public static void double4x2_operator_add_wide_scalar()
        {
            double4x2 a0 = double4x2(-194.51420387742769, 338.54838696985894, 246.97140252169754, 100.51093797595752, -45.724677822424439, -478.11131094308166, 30.916145577522116, 60.37435224483454);
            double b0 = (124.121678171736);
            double4x2 r0 = double4x2(-70.3925257056917, 462.67006514159493, 371.09308069343354, 224.63261614769351, 78.397000349311554, -353.98963277134567, 155.03782374925811, 184.49603041657053);
            TestUtils.AreEqual(r0, a0 + b0);

            double4x2 a1 = double4x2(-242.1187475855084, 6.7993848355483806, -484.69981287638649, -188.26501068298938, -213.52673087526426, -267.78430688929944, 189.25996669999324, 198.53359684652355);
            double b1 = (82.50134495762245);
            double4x2 r1 = double4x2(-159.61740262788595, 89.300729793170831, -402.19846791876404, -105.76366572536693, -131.02538591764181, -185.282961931677, 271.76131165761569, 281.034941804146);
            TestUtils.AreEqual(r1, a1 + b1);

            double4x2 a2 = double4x2(187.53610023648298, 302.10236730338181, 300.39907970111778, 124.02158909850823, -200.16134295247559, 31.37822701007974, 362.52213518811493, -423.98885961248953);
            double b2 = (-424.92567582844089);
            double4x2 r2 = double4x2(-237.38957559195791, -122.82330852505908, -124.5265961273231, -300.90408672993266, -625.08701878091642, -393.54744881836115, -62.403540640325957, -848.91453544093042);
            TestUtils.AreEqual(r2, a2 + b2);

            double4x2 a3 = double4x2(432.41331907380993, -465.69948957194549, -311.04303779781003, 84.918990413154916, -432.44245716204978, 235.75065886031405, -472.63775394514096, -257.57773721291579);
            double b3 = (374.21141474983256);
            double4x2 r3 = double4x2(806.62473382364249, -91.488074822112935, 63.168376952022527, 459.13040516298747, -58.231042412217221, 609.9620736101466, -98.4263391953084, 116.63367753691676);
            TestUtils.AreEqual(r3, a3 + b3);
        }

        [TestCompiler]
        public static void double4x2_operator_add_scalar_wide()
        {
            double a0 = (-340.35468284243473);
            double4x2 b0 = double4x2(511.36225652665007, -146.21663791789518, -106.21042661844308, -363.45024960276214, 199.08958325120136, -27.108407271610758, 419.84900041103788, 284.95503748811552);
            double4x2 r0 = double4x2(171.00757368421534, -486.57132076032991, -446.56510946087781, -703.80493244519687, -141.26509959123337, -367.46309011404549, 79.494317568603151, -55.399645354319205);
            TestUtils.AreEqual(r0, a0 + b0);

            double a1 = (-164.92418129971446);
            double4x2 b1 = double4x2(-249.19032561461921, 150.92817718858282, 298.17509784278229, -457.15341803857751, 424.71807094324288, -301.85750283946163, 230.28885208363124, -423.58759351428023);
            double4x2 r1 = double4x2(-414.11450691433367, -13.99600411113164, 133.25091654306783, -622.077599338292, 259.79388964352842, -466.78168413917609, 65.364670783916779, -588.51177481399463);
            TestUtils.AreEqual(r1, a1 + b1);

            double a2 = (-67.060037882560891);
            double4x2 b2 = double4x2(68.7241366229598, -164.02241833695325, 318.93515339444161, 7.8045504129512437, 187.69836029210046, -3.6569664495331153, -446.0830535581722, -209.28724227160552);
            double4x2 r2 = double4x2(1.6640987403989129, -231.08245621951414, 251.87511551188072, -59.255487469609648, 120.63832240953957, -70.717004332094, -513.14309144073309, -276.34728015416641);
            TestUtils.AreEqual(r2, a2 + b2);

            double a3 = (-38.212905186327589);
            double4x2 b3 = double4x2(-346.25717870623674, 465.60741708502519, -192.18595108398512, 278.69379843338106, 381.97845548297209, 481.24367283342576, -97.228162095522578, -455.51374289743313);
            double4x2 r3 = double4x2(-384.47008389256433, 427.3945118986976, -230.39885627031271, 240.48089324705347, 343.76555029664451, 443.03076764709817, -135.44106728185017, -493.72664808376072);
            TestUtils.AreEqual(r3, a3 + b3);
        }

        [TestCompiler]
        public static void double4x2_operator_sub_wide_wide()
        {
            double4x2 a0 = double4x2(160.4922617229131, 11.223957305412682, 359.20010607279846, -498.22830485656311, -355.25362913462038, -94.534852787170053, -410.46404786150163, -401.38464398001537);
            double4x2 b0 = double4x2(115.46876078260539, -130.98230630298252, 241.54083716196044, 9.9870860623135513, 419.89512582304656, 59.124466208333388, -402.38163847587145, -75.370143687059226);
            double4x2 r0 = double4x2(45.023500940307713, 142.2062636083952, 117.65926891083802, -508.21539091887666, -775.14875495766694, -153.65931899550344, -8.0824093856301715, -326.01450029295614);
            TestUtils.AreEqual(r0, a0 - b0);

            double4x2 a1 = double4x2(317.70681944382693, 447.0604133303558, -489.07414482956477, -230.00838218909149, 24.875419389864192, 366.61447136784648, -107.3741567634857, -219.0081404275299);
            double4x2 b1 = double4x2(320.97960796997859, -73.908757482612884, -31.444742455819949, -389.25194734579509, -375.02884000122026, 259.18275821357167, 276.648654351313, -453.06919905779381);
            double4x2 r1 = double4x2(-3.2727885261516576, 520.96917081296874, -457.62940237374482, 159.2435651567036, 399.90425939108445, 107.4317131542748, -384.02281111479869, 234.06105863026391);
            TestUtils.AreEqual(r1, a1 - b1);

            double4x2 a2 = double4x2(473.90756891384137, 259.63620793988753, -360.119631219711, 7.8096120393879573, 437.42847439154446, -59.1991718091067, 418.74433322378638, 183.14215072576985);
            double4x2 b2 = double4x2(-272.57653225240136, -191.14805301984217, 87.136884968325944, 430.02477594373033, 343.65711538105143, 121.02942067060133, -354.1881703595576, 249.05200373802893);
            double4x2 r2 = double4x2(746.48410116624268, 450.78426095972969, -447.25651618803693, -422.21516390434238, 93.771359010493029, -180.22859247970803, 772.932503583344, -65.909853012259077);
            TestUtils.AreEqual(r2, a2 - b2);

            double4x2 a3 = double4x2(271.23036516421962, 496.20853709439211, 165.35493691514944, -227.40367113212295, -166.52285702830312, 356.14227430715334, 386.92636579411396, -394.63875717420075);
            double4x2 b3 = double4x2(-2.2254426489702723, 22.447240601502017, 478.1129555544411, -320.0629958212669, -111.52409534879217, 222.22894607401872, -245.41106307013473, -119.90228348593337);
            double4x2 r3 = double4x2(273.45580781318989, 473.7612964928901, -312.75801863929166, 92.659324689143943, -54.998761679510949, 133.91332823313462, 632.33742886424875, -274.73647368826738);
            TestUtils.AreEqual(r3, a3 - b3);
        }

        [TestCompiler]
        public static void double4x2_operator_sub_wide_scalar()
        {
            double4x2 a0 = double4x2(207.38960108877609, 248.45773684627272, -384.82393211164697, -205.34476122881506, -374.81156152058929, 191.64204820973896, 18.856238135535364, -44.96160151667965);
            double b0 = (-36.112476604111691);
            double4x2 r0 = double4x2(243.50207769288778, 284.57021345038441, -348.71145550753528, -169.23228462470337, -338.6990849164776, 227.75452481385065, 54.968714739647055, -8.8491249125679587);
            TestUtils.AreEqual(r0, a0 - b0);

            double4x2 a1 = double4x2(480.85798738936796, -366.86545269883493, -35.523088233323335, 349.39776460705218, 439.07729336203886, 490.2222661870635, 195.02405104181923, -384.84940952102158);
            double b1 = (16.338193185784917);
            double4x2 r1 = double4x2(464.51979420358305, -383.20364588461985, -51.861281419108252, 333.05957142126726, 422.73910017625394, 473.88407300127858, 178.68585785603432, -401.1876027068065);
            TestUtils.AreEqual(r1, a1 - b1);

            double4x2 a2 = double4x2(189.05188545447402, -54.931482579061537, 53.088051582261983, 316.80250730961677, -273.80670917863335, 256.88723695319482, 297.17363156805447, 101.82901363346218);
            double b2 = (55.602777745389744);
            double4x2 r2 = double4x2(133.44910770908427, -110.53426032445128, -2.5147261631277615, 261.199729564227, -329.4094869240231, 201.28445920780507, 241.57085382266473, 46.226235888072438);
            TestUtils.AreEqual(r2, a2 - b2);

            double4x2 a3 = double4x2(136.60794765157993, 336.58969966349639, -51.876563334780087, 317.34576311583896, -467.05592773251976, -50.167055391784345, 477.804535373023, -60.821922092149919);
            double b3 = (-19.732211837420323);
            double4x2 r3 = double4x2(156.34015948900026, 356.32191150091671, -32.144351497359764, 337.07797495325929, -447.32371589509944, -30.434843554364022, 497.53674721044331, -41.089710254729596);
            TestUtils.AreEqual(r3, a3 - b3);
        }

        [TestCompiler]
        public static void double4x2_operator_sub_scalar_wide()
        {
            double a0 = (-86.008225719448262);
            double4x2 b0 = double4x2(466.42511413359318, 298.48694219183506, -300.95010652251085, 315.38003006362362, -381.09218543632522, -125.00837546447684, 58.466194418476107, 214.74609361158036);
            double4x2 r0 = double4x2(-552.43333985304139, -384.49516791128332, 214.94188080306259, -401.38825578307188, 295.08395971687696, 39.00014974502858, -144.47442013792437, -300.75431933102863);
            TestUtils.AreEqual(r0, a0 - b0);

            double a1 = (-257.54942739082009);
            double4x2 b1 = double4x2(480.22459505508868, -443.35507723472784, 260.79503858312728, 29.681931747906788, 139.85773164586055, -247.78996216868512, -248.4662297929014, 91.445112509394562);
            double4x2 r1 = double4x2(-737.77402244590871, 185.80564984390776, -518.34446597394731, -287.23135913872687, -397.40715903668064, -9.7594652221349634, -9.0831975979186836, -348.99453990021465);
            TestUtils.AreEqual(r1, a1 - b1);

            double a2 = (86.384162704639266);
            double4x2 b2 = double4x2(373.81828206303453, 260.41195428576873, 114.35393171867076, -464.40545318294573, -109.74146156652898, -311.67535057276268, 107.86401586787031, -258.7951592219971);
            double4x2 r2 = double4x2(-287.43411935839526, -174.02779158112946, -27.9697690140315, 550.789615887585, 196.12562427116825, 398.05951327740195, -21.479853163231041, 345.17932192663636);
            TestUtils.AreEqual(r2, a2 - b2);

            double a3 = (14.097560173877355);
            double4x2 b3 = double4x2(-461.97019527012958, 30.310863747406188, 63.701105862716759, -462.67674634544028, 39.759483117498235, 47.998150132595583, -177.61928113625351, 202.47706017386031);
            double4x2 r3 = double4x2(476.06775544400693, -16.213303573528833, -49.603545688839404, 476.77430651931763, -25.66192294362088, -33.900589958718228, 191.71684131013086, -188.37949999998295);
            TestUtils.AreEqual(r3, a3 - b3);
        }

        [TestCompiler]
        public static void double4x2_operator_mul_wide_wide()
        {
            double4x2 a0 = double4x2(-482.71381710596097, -407.29348559272171, 137.70058995937029, 208.54113278563182, 194.296573967811, -484.24241684574747, 183.98730739578014, -241.33547770294149);
            double4x2 b0 = double4x2(-236.36788355389979, 260.72759139757954, -416.38629718142852, -364.49561541364324, -253.14750897751537, -369.20287220981106, 193.54791531038836, 169.08491976982214);
            double4x2 r0 = double4x2(114098.04331156026, -106192.64949051509, -57336.638772880389, -76012.328533757158, -49185.69370281692, 178783.69114527057, 35610.359790024842, -40806.189885013562);
            TestUtils.AreEqual(r0, a0 * b0);

            double4x2 a1 = double4x2(45.868758938214114, 363.32610266438041, -328.11893692990714, -471.02307413100408, -262.68257415605831, -379.26274674910246, -374.09058182970182, 481.44738720424812);
            double4x2 b1 = double4x2(201.96966442930034, 249.45608317547294, -308.19319810913555, -385.57964843585137, -183.27959522198864, 22.275629292370581, -265.52144229855458, -95.677454277722859);
            double4x2 r1 = double4x2(9264.0978505395742, 90633.9064860661, 101124.02453259782, 181616.91132860651, 48144.355863192381, -8448.3163509892329, 99329.070837727879, -46063.660376363579);
            TestUtils.AreEqual(r1, a1 * b1);

            double4x2 a2 = double4x2(104.62807397946165, 412.93539948618752, 477.87724731763694, 20.377821216535722, 291.99596299417124, -138.48832399141429, -393.46498483860165, 9.36312318284206);
            double4x2 b2 = double4x2(133.25437146669924, 148.31146080247663, 249.284127113076, 500.00547503866505, -19.331578978957396, -36.691062705913112, 30.5238278054278, -401.36701054189678);
            double4x2 r2 = double4x2(13942.148235904471, 61243.052314850727, 119127.21246477668, 10189.022177626932, -5644.7430201585421, 5081.2837796057929, -12010.057444678736, -3758.048761232847);
            TestUtils.AreEqual(r2, a2 * b2);

            double4x2 a3 = double4x2(-131.94228917543882, 364.44964258952518, 390.61597866128011, 418.79794974755396, -277.34480942289565, 11.410165553637853, 474.87644956767394, -502.40503358394142);
            double4x2 b3 = double4x2(3.4372422711165882, 257.24176681099539, -290.97193516929258, 337.47938100317469, 490.28616284312966, -191.01981481864107, -325.73449650673871, -52.181983733634468);
            double4x2 r3 = double4x2(-453.51761370170692, 93751.669973365249, -113658.28721911977, 141335.67284620318, -135978.32239641057, -2179.56771110594, -154683.64120283397, 26216.491290173308);
            TestUtils.AreEqual(r3, a3 * b3);
        }

        [TestCompiler]
        public static void double4x2_operator_mul_wide_scalar()
        {
            double4x2 a0 = double4x2(-96.318821236639678, -277.14229239017811, -239.93690191951436, 509.53140544776409, 255.85810172551226, 215.73149667295229, -455.50827500573746, -389.24327367788334);
            double b0 = (-301.20720424373042);
            double4x2 r0 = double4x2(29011.922860739887, 83477.255068544036, 72270.723422079071, -153474.5301092997, -77066.303503849529, -64979.880980175592, 137202.37402436248, 117242.87823519246);
            TestUtils.AreEqual(r0, a0 * b0);

            double4x2 a1 = double4x2(-338.29248658674419, 243.75734459783757, 135.35469991311186, -207.35010275959507, -383.93960946795517, -31.425238862366086, 42.676120539510634, 260.38388049806645);
            double b1 = (53.796284939067618);
            double4x2 r1 = double4x2(-18198.879001166202, 13113.239565975766, 7281.5800043677564, -11154.665210200128, -20654.52463033246, -1690.5611041181071, 2295.81674063751, 14007.685428814115);
            TestUtils.AreEqual(r1, a1 * b1);

            double4x2 a2 = double4x2(176.86755927692525, -290.50059689697838, 207.09101805793637, -156.52330858843555, -208.4020064847553, 370.94506400215676, -341.59844247512444, 10.270311121954705);
            double b2 = (25.672123205695357);
            double4x2 r2 = double4x2(4540.5657728478518, -7457.7671148672716, 5316.4661303762241, -4018.2856626453918, -5350.1219867907612, 9522.9473856079185, -8769.5573020950324, 263.66069248364448);
            TestUtils.AreEqual(r2, a2 * b2);

            double4x2 a3 = double4x2(-176.88876565587185, 186.27978214355176, -487.65221785365242, -129.37681800191143, -317.71628990663044, -207.62735686433842, 388.87138933170183, -233.33533274072005);
            double b3 = (-61.006107120311867);
            double4x2 r3 = double4x2(10791.294985981862, -11364.204343797875, 29749.763439837578, 7892.7760179097013, 19382.634015911957, 12666.53677397305, -23723.529633594302, 14234.88030413398);
            TestUtils.AreEqual(r3, a3 * b3);
        }

        [TestCompiler]
        public static void double4x2_operator_mul_scalar_wide()
        {
            double a0 = (37.432166355397612);
            double4x2 b0 = double4x2(96.747546479454058, 492.18539427788244, -274.05458534604617, -452.87096926796761, 420.85330434369541, 102.18292694081686, -114.94887762654054, -351.12003843445336);
            double4x2 r0 = double4x2(3621.4702542954869, 18423.565556306661, -10258.456829132712, -16951.941459168724, 15753.450899411988, 3824.9283199300971, -4302.785509682908, -13143.183689392061);
            TestUtils.AreEqual(r0, a0 * b0);

            double a1 = (-464.66496799172131);
            double4x2 b1 = double4x2(444.08484646495663, 447.10525605040846, 130.82935124767448, -321.41334191030512, 445.30131861441828, 478.24357317306271, 358.57170622356784, -144.89011222910608);
            double4x2 r1 = double4x2(-206350.67096824755, -207754.14949159342, -60791.816309878326, 149349.52023086412, -206915.92296063996, -222223.03462070762, -166615.71039511106, 67325.359361254479);
            TestUtils.AreEqual(r1, a1 * b1);

            double a2 = (-438.89383741789209);
            double4x2 b2 = double4x2(-3.536441089369589, -471.80755470311624, -42.560401697904069, 119.91104155402218, 271.9000023677479, 239.6840079946835, 487.44143389511919, -79.188288010278825);
            double4x2 r2 = double4x2(1552.1222005157297, 207073.42820640272, 18679.498023240089, -52628.217176421109, -119335.23543311482, -105195.83403648737, -213935.04143870863, 34755.251603384531);
            TestUtils.AreEqual(r2, a2 * b2);

            double a3 = (-112.92564468873928);
            double4x2 b3 = double4x2(161.3700478828373, 459.75914332818195, -337.19599811043406, -276.83451689259823, 469.72386405883537, -274.56515110403541, 506.78586625810055, 65.882571966332648);
            double4x2 r3 = double4x2(-18222.81669062213, -51918.597661877429, 38078.075473083678, 31261.716292192341, -53043.870174529715, 31005.446697484316, -57229.120666337207, -7439.831913050376);
            TestUtils.AreEqual(r3, a3 * b3);
        }

        [TestCompiler]
        public static void double4x2_operator_div_wide_wide()
        {
            double4x2 a0 = double4x2(-353.13144390337703, -102.79985456485292, 51.319128298814917, -191.87167868012176, 8.0418245829836223, -128.73764210973758, -136.05959779399427, -370.4710053738537);
            double4x2 b0 = double4x2(-178.73954805114283, -302.09628381491467, -199.40583739029518, 278.85077561012042, 502.33758782890516, -361.48483078623417, 353.121059820578, -38.894930142394685);
            double4x2 r0 = double4x2(1.97567604793504, 0.34028837848212429, -0.25736021056579439, -0.68808013268139567, 0.016008805189634039, 0.35613566917796119, -0.3853058151307277, 9.5249176182488586);
            TestUtils.AreEqual(r0, a0 / b0);

            double4x2 a1 = double4x2(-237.69456326109105, -432.54687496300176, 200.26549181727012, 361.44157068871039, -416.22613234828509, -450.01919362042992, -273.49744594911925, -286.90817011841955);
            double4x2 b1 = double4x2(-75.764737402910725, -195.21784719974636, -405.03399224068687, -394.2300085473014, -375.82771342612227, -121.24548655433836, 447.623344391409, 338.28628007429018);
            double4x2 r1 = double4x2(3.1372716570909582, 2.2157137842034547, -0.49444119667433889, -0.9168291678773689, 1.1074918572499153, 3.7116366671409717, -0.61099906735420106, -0.84812239519560884);
            TestUtils.AreEqual(r1, a1 / b1);

            double4x2 a2 = double4x2(-314.25606241554772, 177.76210340194507, 97.626988217992221, -68.107280047660367, -386.45074027890837, 263.69934690357161, -297.0270885420158, -501.77703046322659);
            double4x2 b2 = double4x2(-405.54420752336466, -431.16893526127978, 296.20513095343722, 437.939790691221, 39.21061684527001, 331.2897075765253, -310.61955156485533, 207.26946959610541);
            double4x2 r2 = double4x2(0.77489964493560781, -0.41227947763496636, 0.32959249525403717, -0.15551745124635385, -9.855767936625206, 0.79597808465769837, 0.95624080018671487, -2.420892143165184);
            TestUtils.AreEqual(r2, a2 / b2);

            double4x2 a3 = double4x2(-263.40686071263946, -451.08085248017721, -416.34552903489464, -315.27873411554788, -28.181118739853218, -397.87015146662952, -261.38664376986526, 40.348221559239619);
            double4x2 b3 = double4x2(-223.2929938879297, -480.091406807346, 448.67593666942605, -460.0974516626901, -220.56984601755153, -84.853158275062754, 441.3738078742166, 72.418480191574645);
            double4x2 r3 = double4x2(1.1796467776541293, 0.93957285234474042, -0.92794263076704353, 0.68524338262731188, 0.12776505605218016, 4.6889256635195675, -0.59221149761645042, 0.55715366371267527);
            TestUtils.AreEqual(r3, a3 / b3);
        }

        [TestCompiler]
        public static void double4x2_operator_div_wide_scalar()
        {
            double4x2 a0 = double4x2(171.34242184988341, 0.10338377957384637, 57.888263967767443, -256.13074529177078, 95.6696842162263, -290.38690461329509, -127.44869118903239, -79.7448890580539);
            double b0 = (171.79682191265601);
            double4x2 r0 = double4x2(0.99735501473360411, 0.00060177934855167557, 0.33695771157628673, -1.4908933846400916, 0.55687691513214455, -1.6902926455818372, -0.74185709473618289, -0.46418139852783397);
            TestUtils.AreEqual(r0, a0 / b0);

            double4x2 a1 = double4x2(146.46688110496234, 58.686315802245531, -453.20579859856787, -205.03382143985192, 481.73814247629514, 464.47907159499778, -293.46349753693841, -158.50557930697948);
            double b1 = (-499.84355687529012);
            double4x2 r1 = double4x2(-0.29302544584265894, -0.11740936738109768, 0.906695289685692, 0.41019598756377973, -0.96377783778552883, -0.92924889239071318, 0.587110693936897, 0.31711037809081188);
            TestUtils.AreEqual(r1, a1 / b1);

            double4x2 a2 = double4x2(-289.5822156824089, 203.58342680874443, 180.97040160976837, 259.11918723728468, 460.84470603468117, 490.95625924084163, -280.47805536933151, -320.24387112271222);
            double b2 = (494.12860535743118);
            double4x2 r2 = double4x2(-0.58604624897791069, 0.41200494082199718, 0.3662414999812898, 0.5243962491300197, 0.93264122141102535, 0.99357991809785062, -0.5676215712434739, -0.64809822311554233);
            TestUtils.AreEqual(r2, a2 / b2);

            double4x2 a3 = double4x2(192.41448912043802, 226.85298524929817, -192.23568949114332, 460.97652957447644, -437.89221760159927, -413.23271794488312, 249.47184693509337, 313.03501739773662);
            double b3 = (264.80085885934568);
            double4x2 r3 = double4x2(0.72663846314276059, 0.85669278501017143, -0.7259632401466386, 1.74084227505974, -1.6536661530776777, -1.5605414564171789, 0.94211116991733534, 1.1821525758872689);
            TestUtils.AreEqual(r3, a3 / b3);
        }

        [TestCompiler]
        public static void double4x2_operator_div_scalar_wide()
        {
            double a0 = (-264.44250095283729);
            double4x2 b0 = double4x2(105.58908157497137, -142.34910137129441, -288.94890679463231, 39.644133824689334, -363.99138396046658, -149.71822006521666, -395.72912306139671, 258.71868693955184);
            double4x2 r0 = double4x2(-2.5044492954044237, 1.85770404172122, 0.915187753732487, -6.670406827961755, 0.72650758398599513, 1.7662679988958405, 0.66824119212426392, -1.0221236976771717);
            TestUtils.AreEqual(r0, a0 / b0);

            double a1 = (-9.6662514254759344);
            double4x2 b1 = double4x2(117.72553282497711, -331.38655797177296, -509.98602676297821, 427.8964666928614, 467.61712882836218, -407.12461943511136, 252.69070994699871, 444.59937664708093);
            double4x2 r1 = double4x2(-0.082108368452634473, 0.029169111398595994, 0.018953953477569365, -0.022590164158598314, -0.020671294590288436, 0.023742733708631857, -0.0382532916524846, -0.021741486680375892);
            TestUtils.AreEqual(r1, a1 / b1);

            double a2 = (-88.313306134340053);
            double4x2 b2 = double4x2(199.95503411067421, -218.34692607556792, -13.417186028052697, -296.13107575854804, 0.561349630617201, -289.29929865957206, 196.21833929615946, 334.73346845001606);
            double4x2 r2 = double4x2(-0.44166583015588912, 0.40446324444144272, 6.5821034268805914, 0.29822370350063077, -157.32317492974929, 0.30526622962284194, -0.45007671785992226, -0.26383171824220319);
            TestUtils.AreEqual(r2, a2 / b2);

            double a3 = (-282.39273203648293);
            double4x2 b3 = double4x2(-479.50358436978587, -473.43943927876626, 105.0507777226394, -287.63127841038227, 77.299297130340392, -210.89436421678141, -184.0682357214709, -315.14843645465953);
            double4x2 r3 = double4x2(0.588927259861119, 0.59647065412775435, -2.6881546063568527, 0.98178728543414817, -3.6532380308752153, 1.3390245542370554, 1.5341741660619548, 0.89606261485327354);
            TestUtils.AreEqual(r3, a3 / b3);
        }

        [TestCompiler]
        public static void double4x2_operator_mod_wide_wide()
        {
            double4x2 a0 = double4x2(-388.81249422059045, 181.68118842955732, -167.07872470052854, 432.82015319951813, -258.43895995730486, -170.11079629236406, 283.318293464984, 122.71651297561664);
            double4x2 b0 = double4x2(436.94417187056695, 58.940049437312382, -201.11623368091705, 279.2893537391393, -397.07975954426445, 377.89994758083481, 174.69386657266591, -228.17652736798698);
            double4x2 r0 = double4x2(-388.81249422059045, 4.8610401176201776, -167.07872470052854, 153.53079946037883, -258.43895995730486, -170.11079629236406, 108.62442689231807, 122.71651297561664);
            TestUtils.AreEqual(r0, a0 % b0);

            double4x2 a1 = double4x2(335.27101413126616, -503.60851668920765, 191.02251848532933, 289.74269379756538, -124.03371745163281, 259.27395761165485, -274.35845030208975, -140.03080398404541);
            double4x2 b1 = double4x2(-317.06019106370405, -417.48011107811709, -249.9759434433542, -397.57157177364991, -358.74544947163452, -198.1592100589346, 208.73709378425826, -12.119406944196385);
            double4x2 r1 = double4x2(18.210823067562103, -86.128405611090557, 191.02251848532933, 289.74269379756538, -124.03371745163281, 61.114747552720246, -65.621356517831487, -6.7173275978851734);
            TestUtils.AreEqual(r1, a1 % b1);

            double4x2 a2 = double4x2(324.5775689205982, -200.51308903494527, 211.42317328761476, -51.272212767634642, -230.63392483006879, 99.989400671790122, 399.18986649028489, 24.903281461868119);
            double4x2 b2 = double4x2(25.27141596063575, -194.12068495253135, -493.8717965995296, -312.3016990685378, -216.98060546488529, 413.57096047586344, -436.39440151508637, 3.4912750737235);
            double4x2 r2 = double4x2(21.3205773929692, -6.3924040824139183, 211.42317328761476, -51.272212767634642, -13.653319365183506, 99.989400671790122, 399.18986649028489, 0.46435594580361794);
            TestUtils.AreEqual(r2, a2 % b2);

            double4x2 a3 = double4x2(50.92402961241271, -364.86367886367429, -252.62662398658068, -281.28977955565313, -364.79852192699843, -329.02623311105475, 51.6098087074281, 41.647804041229051);
            double4x2 b3 = double4x2(-308.23343076754054, -441.37506195594324, 84.6008532441225, 373.16344922276369, 67.252760203207231, -320.33327522889397, 118.97936325845274, 44.823946258436877);
            double4x2 r3 = double4x2(50.92402961241271, -364.86367886367429, -83.424917498335674, -281.28977955565313, -28.534720910962278, -8.6929578821607834, 51.6098087074281, 41.647804041229051);
            TestUtils.AreEqual(r3, a3 % b3);
        }

        [TestCompiler]
        public static void double4x2_operator_mod_wide_scalar()
        {
            double4x2 a0 = double4x2(-244.49962889612635, -211.81931958525411, -145.92677576184587, -304.91822090042672, 155.47946436492703, -133.90778428591221, 281.30965412841624, -226.53575311719243);
            double b0 = (39.634963769295723);
            double4x2 r0 = double4x2(-6.6898462803520147, -13.644500738775491, -27.021884453958705, -27.473474515356656, 36.574573057039856, -15.002892978025045, 3.86490774334618, -28.360934270713813);
            TestUtils.AreEqual(r0, a0 % b0);

            double4x2 a1 = double4x2(335.16613046041039, 319.47152033423606, -285.40231646476423, -355.84685985923136, 259.37800061860025, -330.87193957477433, -284.34358109363518, -102.68343811048356);
            double b1 = (101.70649032560482);
            double4x2 r1 = double4x2(30.046659483595931, 14.352049357421606, -81.98933581355459, -50.7273888824169, 55.965019967390617, -25.752468597959876, -80.930600442425543, -0.976947784878746);
            TestUtils.AreEqual(r1, a1 % b1);

            double4x2 a2 = double4x2(-172.14173921017988, -416.71365447375626, -339.256669917729, 435.29751440291182, 132.55290490600885, 226.94410215455298, -306.11827268550093, 115.43844633709568);
            double b2 = (206.41684517935698);
            double4x2 r2 = double4x2(-172.14173921017988, -3.8799641150422985, -132.83982473837204, 22.46382404419785, 132.55290490600885, 20.527256975195996, -99.701427506143943, 115.43844633709568);
            TestUtils.AreEqual(r2, a2 % b2);

            double4x2 a3 = double4x2(281.88292015804109, -140.04050237501065, -462.32346961569203, -211.60869822819188, 351.33104555277669, 321.04701176334504, 346.08518497370426, -94.407745643708722);
            double b3 = (-218.3474491659307);
            double4x2 r3 = double4x2(63.53547099211039, -140.04050237501065, -25.628571283830638, -211.60869822819188, 132.983596386846, 102.69956259741434, 127.73773580777356, -94.407745643708722);
            TestUtils.AreEqual(r3, a3 % b3);
        }

        [TestCompiler]
        public static void double4x2_operator_mod_scalar_wide()
        {
            double a0 = (-66.945025236785909);
            double4x2 b0 = double4x2(-249.77609479137516, -396.07375664081133, 386.49204582091977, 168.93948109864232, -199.4182442163202, 261.7517141130528, 16.127438791155555, 257.66814744550186);
            double4x2 r0 = double4x2(-66.945025236785909, -66.945025236785909, -66.945025236785909, -66.945025236785909, -66.945025236785909, -66.945025236785909, -2.4352700721636893, -66.945025236785909);
            TestUtils.AreEqual(r0, a0 % b0);

            double a1 = (-75.788451945310669);
            double4x2 b1 = double4x2(170.95630439136005, -242.85828005655588, 425.94531913564788, 303.27240409668184, 3.033060790520608, -505.74352788633831, 461.95706126743789, 205.97275672013529);
            double4x2 r1 = double4x2(-75.788451945310669, -75.788451945310669, -75.788451945310669, -75.788451945310669, -2.9949929728160782, -75.788451945310669, -75.788451945310669, -75.788451945310669);
            TestUtils.AreEqual(r1, a1 % b1);

            double a2 = (270.04063642678807);
            double4x2 b2 = double4x2(-47.480711720642034, -150.254496405951, 149.49949009227544, -220.29804263836616, 31.118842377848409, 400.63568348467152, 6.2314283876826266, -39.050740021770252);
            double4x2 r2 = double4x2(32.6370778235779, 119.78614002083708, 120.54114633451263, 49.742593788421914, 21.089897404000794, 270.04063642678807, 2.0892157564351237, 35.736196296166554);
            TestUtils.AreEqual(r2, a2 % b2);

            double a3 = (-71.941097054603063);
            double4x2 b3 = double4x2(-495.30713843521994, -86.71961859926563, -436.97006365143233, -472.2947320753218, -130.00875359867177, -251.51684605866524, 281.97637022751212, 388.86081928241106);
            double4x2 r3 = double4x2(-71.941097054603063, -71.941097054603063, -71.941097054603063, -71.941097054603063, -71.941097054603063, -71.941097054603063, -71.941097054603063, -71.941097054603063);
            TestUtils.AreEqual(r3, a3 % b3);
        }

        [TestCompiler]
        public static void double4x2_operator_plus()
        {
            double4x2 a0 = double4x2(-418.82956357432045, -405.79894823851015, -34.041791216489742, 236.99924456188421, -459.83910129025537, 210.8614223985287, 293.74197902052754, -373.015422279488);
            double4x2 r0 = double4x2(-418.82956357432045, -405.79894823851015, -34.041791216489742, 236.99924456188421, -459.83910129025537, 210.8614223985287, 293.74197902052754, -373.015422279488);
            TestUtils.AreEqual(r0, +a0);

            double4x2 a1 = double4x2(-386.059833944803, -418.64524932328857, 504.47483062393724, -170.74650843941907, 439.55937572920664, -478.74939916969714, 116.40075665172219, 421.40964742256779);
            double4x2 r1 = double4x2(-386.059833944803, -418.64524932328857, 504.47483062393724, -170.74650843941907, 439.55937572920664, -478.74939916969714, 116.40075665172219, 421.40964742256779);
            TestUtils.AreEqual(r1, +a1);

            double4x2 a2 = double4x2(-258.5960806620289, 124.16434031546316, 222.17254386757156, -65.949277193261878, 239.04183947250328, 498.4495329793773, -139.382530515726, 279.07295549990283);
            double4x2 r2 = double4x2(-258.5960806620289, 124.16434031546316, 222.17254386757156, -65.949277193261878, 239.04183947250328, 498.4495329793773, -139.382530515726, 279.07295549990283);
            TestUtils.AreEqual(r2, +a2);

            double4x2 a3 = double4x2(108.7758186370022, 136.81214934997831, -236.03003965878395, -440.3083276414817, 342.2791270419392, 102.4722116470673, -161.454825714908, -355.27087919566355);
            double4x2 r3 = double4x2(108.7758186370022, 136.81214934997831, -236.03003965878395, -440.3083276414817, 342.2791270419392, 102.4722116470673, -161.454825714908, -355.27087919566355);
            TestUtils.AreEqual(r3, +a3);
        }

        [TestCompiler]
        public static void double4x2_operator_neg()
        {
            double4x2 a0 = double4x2(148.46174890755753, -467.12267873581624, 132.04719954917539, 183.52262290917463, 473.7010145009034, -407.99109024926605, -54.958759571872065, -382.98981803608581);
            double4x2 r0 = double4x2(-148.46174890755753, 467.12267873581624, -132.04719954917539, -183.52262290917463, -473.7010145009034, 407.99109024926605, 54.958759571872065, 382.98981803608581);
            TestUtils.AreEqual(r0, -a0);

            double4x2 a1 = double4x2(-299.09338893512887, 407.70980305583669, 168.73550351370852, 466.44152829909763, 171.90249474900895, -280.55831564616335, -78.85761622286293, 318.69633522569029);
            double4x2 r1 = double4x2(299.09338893512887, -407.70980305583669, -168.73550351370852, -466.44152829909763, -171.90249474900895, 280.55831564616335, 78.85761622286293, -318.69633522569029);
            TestUtils.AreEqual(r1, -a1);

            double4x2 a2 = double4x2(-39.91539694737429, 132.19563180403577, -505.89525127126615, 410.38058466947666, -237.05693375182193, -137.617827241131, -245.34998547534923, 422.52133222227974);
            double4x2 r2 = double4x2(39.91539694737429, -132.19563180403577, 505.89525127126615, -410.38058466947666, 237.05693375182193, 137.617827241131, 245.34998547534923, -422.52133222227974);
            TestUtils.AreEqual(r2, -a2);

            double4x2 a3 = double4x2(-434.57134386271764, -466.56631515294606, 426.89450116962871, 146.64955885086658, -391.37208408460583, 423.23773809114368, 254.29757296959758, -114.84889536483627);
            double4x2 r3 = double4x2(434.57134386271764, 466.56631515294606, -426.89450116962871, -146.64955885086658, 391.37208408460583, -423.23773809114368, -254.29757296959758, 114.84889536483627);
            TestUtils.AreEqual(r3, -a3);
        }

        [TestCompiler]
        public static void double4x2_operator_prefix_inc()
        {
            double4x2 a0 = double4x2(-139.84208137348389, -56.743654039103376, -381.955324589254, 509.79634380237962, -222.89634452708827, 210.31986556310198, -392.73151058365193, -300.19410218866267);
            double4x2 r0 = double4x2(-138.84208137348389, -55.743654039103376, -380.955324589254, 510.79634380237962, -221.89634452708827, 211.31986556310198, -391.73151058365193, -299.19410218866267);
            TestUtils.AreEqual(r0, ++a0);

            double4x2 a1 = double4x2(362.21273939787068, 130.90919429199266, -450.23016402229212, 243.54693114177644, 46.19202735190845, -41.497298975241051, 299.18547000511808, 154.35656530892311);
            double4x2 r1 = double4x2(363.21273939787068, 131.90919429199266, -449.23016402229212, 244.54693114177644, 47.19202735190845, -40.497298975241051, 300.18547000511808, 155.35656530892311);
            TestUtils.AreEqual(r1, ++a1);

            double4x2 a2 = double4x2(-281.23327435237974, 92.957765384091886, 448.60215565590283, -295.58701171334229, 18.499063262016989, -215.71113381893895, 471.94723651928234, 257.07660090973445);
            double4x2 r2 = double4x2(-280.23327435237974, 93.957765384091886, 449.60215565590283, -294.58701171334229, 19.499063262016989, -214.71113381893895, 472.94723651928234, 258.07660090973445);
            TestUtils.AreEqual(r2, ++a2);

            double4x2 a3 = double4x2(41.625937719655212, 243.00478588929627, -472.61902330472088, -125.7202084649914, -477.45955227197129, 9.8914859340952717, -76.922842299995409, -29.767583622488928);
            double4x2 r3 = double4x2(42.625937719655212, 244.00478588929627, -471.61902330472088, -124.7202084649914, -476.45955227197129, 10.891485934095272, -75.922842299995409, -28.767583622488928);
            TestUtils.AreEqual(r3, ++a3);
        }

        [TestCompiler]
        public static void double4x2_operator_postfix_inc()
        {
            double4x2 a0 = double4x2(-396.6697396695007, 511.20749378167443, 249.11127030528678, -128.81731301584153, -259.49027669592306, 278.00817764830219, -81.393423356764686, 66.719732554033271);
            double4x2 r0 = double4x2(-396.6697396695007, 511.20749378167443, 249.11127030528678, -128.81731301584153, -259.49027669592306, 278.00817764830219, -81.393423356764686, 66.719732554033271);
            TestUtils.AreEqual(r0, a0++);

            double4x2 a1 = double4x2(167.85212691493894, -326.10758486674524, 41.033564825092185, 128.5304239394751, 73.155582223625629, -60.132380275117384, -446.22976490772783, -296.93783797739906);
            double4x2 r1 = double4x2(167.85212691493894, -326.10758486674524, 41.033564825092185, 128.5304239394751, 73.155582223625629, -60.132380275117384, -446.22976490772783, -296.93783797739906);
            TestUtils.AreEqual(r1, a1++);

            double4x2 a2 = double4x2(267.29380071689081, 49.200223230384381, -326.64314738225335, -510.86424064583343, 471.64748762159024, -171.01308186865089, 310.72735967800361, -298.91717185588425);
            double4x2 r2 = double4x2(267.29380071689081, 49.200223230384381, -326.64314738225335, -510.86424064583343, 471.64748762159024, -171.01308186865089, 310.72735967800361, -298.91717185588425);
            TestUtils.AreEqual(r2, a2++);

            double4x2 a3 = double4x2(489.98497008252184, 290.69102896875279, 117.1923401901463, 164.44293578175962, 412.36778874526158, -229.38657079887884, 239.59693848322934, 36.624316947825378);
            double4x2 r3 = double4x2(489.98497008252184, 290.69102896875279, 117.1923401901463, 164.44293578175962, 412.36778874526158, -229.38657079887884, 239.59693848322934, 36.624316947825378);
            TestUtils.AreEqual(r3, a3++);
        }

        [TestCompiler]
        public static void double4x2_operator_prefix_dec()
        {
            double4x2 a0 = double4x2(123.12869626056806, 256.8437465433235, 156.33078844674435, 461.73742530389563, 325.86799755965728, 392.01561731473339, 187.87412580655609, -236.2252043393558);
            double4x2 r0 = double4x2(122.12869626056806, 255.8437465433235, 155.33078844674435, 460.73742530389563, 324.86799755965728, 391.01561731473339, 186.87412580655609, -237.2252043393558);
            TestUtils.AreEqual(r0, --a0);

            double4x2 a1 = double4x2(125.10963517292851, 45.536655685648611, 376.04684680329956, -363.07547991493504, -22.028951416736902, 248.79012667797042, 168.0950144120003, 168.26565011230559);
            double4x2 r1 = double4x2(124.10963517292851, 44.536655685648611, 375.04684680329956, -364.07547991493504, -23.028951416736902, 247.79012667797042, 167.0950144120003, 167.26565011230559);
            TestUtils.AreEqual(r1, --a1);

            double4x2 a2 = double4x2(-190.284744112885, 183.95795854551625, 485.69469259944492, -460.73930261132273, 89.569894117102876, -267.42982090051743, 201.75623450137505, -141.21688682456357);
            double4x2 r2 = double4x2(-191.284744112885, 182.95795854551625, 484.69469259944492, -461.73930261132273, 88.569894117102876, -268.42982090051743, 200.75623450137505, -142.21688682456357);
            TestUtils.AreEqual(r2, --a2);

            double4x2 a3 = double4x2(-217.48409782046645, -213.54412732531506, 180.74062570405226, -128.31251412644633, 478.04553888647149, -454.56614062495817, -386.89835256473083, 387.85698408068015);
            double4x2 r3 = double4x2(-218.48409782046645, -214.54412732531506, 179.74062570405226, -129.31251412644633, 477.04553888647149, -455.56614062495817, -387.89835256473083, 386.85698408068015);
            TestUtils.AreEqual(r3, --a3);
        }

        [TestCompiler]
        public static void double4x2_operator_postfix_dec()
        {
            double4x2 a0 = double4x2(379.68831723727669, 302.69287814884115, -176.07134040448409, -291.25267066212962, 470.56758401848731, -402.92594666170231, -63.655158787805192, 355.26110069605568);
            double4x2 r0 = double4x2(379.68831723727669, 302.69287814884115, -176.07134040448409, -291.25267066212962, 470.56758401848731, -402.92594666170231, -63.655158787805192, 355.26110069605568);
            TestUtils.AreEqual(r0, a0--);

            double4x2 a1 = double4x2(-27.889220489137415, 156.14034969924967, 479.94519613680677, -200.30429491787419, -445.0269393609031, 407.42034907239508, 327.67032519340069, 48.0602071509046);
            double4x2 r1 = double4x2(-27.889220489137415, 156.14034969924967, 479.94519613680677, -200.30429491787419, -445.0269393609031, 407.42034907239508, 327.67032519340069, 48.0602071509046);
            TestUtils.AreEqual(r1, a1--);

            double4x2 a2 = double4x2(-209.66798100698179, 283.941595924991, -94.802087112703418, 152.51066334196867, -287.262531175866, -215.94803939384781, -407.04635567546188, 159.23357136511879);
            double4x2 r2 = double4x2(-209.66798100698179, 283.941595924991, -94.802087112703418, 152.51066334196867, -287.262531175866, -215.94803939384781, -407.04635567546188, 159.23357136511879);
            TestUtils.AreEqual(r2, a2--);

            double4x2 a3 = double4x2(-359.45648663093175, -278.93379868144814, 289.91284073978329, 402.03954691534841, 470.71654937729079, -208.56061873611094, 145.89674789546837, -296.79095258228062);
            double4x2 r3 = double4x2(-359.45648663093175, -278.93379868144814, 289.91284073978329, 402.03954691534841, 470.71654937729079, -208.56061873611094, 145.89674789546837, -296.79095258228062);
            TestUtils.AreEqual(r3, a3--);
        }

        [TestCase /* For player builds */]
        public static void double4x2_EqualsObjectOverride()
        {
            TestUtils.IsFalse(new double4x2().Equals((object)new int()));
            TestUtils.IsTrue(new double4x2().Equals((object)new double4x2()));
        }


    }
}
