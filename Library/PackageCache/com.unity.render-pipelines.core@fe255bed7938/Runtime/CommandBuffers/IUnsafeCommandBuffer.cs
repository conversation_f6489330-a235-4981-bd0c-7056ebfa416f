
using System;
using System.Collections.Generic;
using Unity.Collections;
using UnityEngine.Profiling;
using Unity.Profiling;
using UnityEngine.Rendering.RenderGraphModule;

// NOTE  NOTE  NOTE  NOTE  NOTE  NOTE  NOTE  NOTE  NOTE
//
// This file is automatically generated by reflection on the UnityEngine.Rendering.CommandBuffer type.
// If you changed the command buffer and want to expose the changes here please open and SRP project
// "Edit/Rendering/Generate Core CommandBuffers" menu option.
// This will generate the new command buffer C# files in the project root.
//
// Note that while automated,this doesn't mean you won't have to think. Please consider any new methods on the command
// buffer if they are safe to be executed on the async compute queue or not, if they can be executed inside a
// native render pass or not,... and add the function to the appropriate lists in CommandBufferGenerator.cs in the
// com.unity.render-pipelines.core\Editor\CommandBuffers\CommandBufferGenerator\CommandBufferGenerator.cs.
// If you are unsure please ask the RenderGraph package owners for advise.
//
// Once generated, review the generated file and move the approved files into:
// <unity root>\Packages\com.unity.render-pipelines.core\Runtime\CommandBuffers\
//
// NOTE  NOTE  NOTE  NOTE  NOTE  NOTE  NOTE  NOTE  NOTE
namespace UnityEngine.Rendering
{
    /// <summary>
    /// This interface declares functions that are specific to an unsafe command buffer.
    /// </summary>
    public interface IUnsafeCommandBuffer : IBaseCommandBuffer, IRasterCommandBuffer, IComputeCommandBuffer
    {

        /// <summary>Wraps [Clear](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.Clear.html) on a CommandBuffer.</summary>

        public void Clear() ;

        /// <summary>Wraps [SetRenderTarget](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html) on a CommandBuffer.</summary>
        /// <param name="rt">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        public void SetRenderTarget(RenderTargetIdentifier rt) ;

        /// <summary>Wraps [SetRenderTarget](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html) on a CommandBuffer.</summary>
        /// <param name="rt">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="loadAction">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="storeAction">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        public void SetRenderTarget(RenderTargetIdentifier rt, RenderBufferLoadAction loadAction, RenderBufferStoreAction storeAction) ;

        /// <summary>Wraps [SetRenderTarget](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html) on a CommandBuffer.</summary>
        /// <param name="rt">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="colorLoadAction">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="colorStoreAction">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="depthLoadAction">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="depthStoreAction">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        public void SetRenderTarget(RenderTargetIdentifier rt, RenderBufferLoadAction colorLoadAction, RenderBufferStoreAction colorStoreAction, RenderBufferLoadAction depthLoadAction, RenderBufferStoreAction depthStoreAction) ;

        /// <summary>Wraps [SetRenderTarget](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html) on a CommandBuffer.</summary>
        /// <param name="rt">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="mipLevel">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        public void SetRenderTarget(RenderTargetIdentifier rt, int mipLevel) ;

        /// <summary>Wraps [SetRenderTarget](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html) on a CommandBuffer.</summary>
        /// <param name="rt">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="mipLevel">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="cubemapFace">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        public void SetRenderTarget(RenderTargetIdentifier rt, int mipLevel, CubemapFace cubemapFace) ;

        /// <summary>Wraps [SetRenderTarget](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html) on a CommandBuffer.</summary>
        /// <param name="rt">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="mipLevel">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="cubemapFace">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="depthSlice">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        public void SetRenderTarget(RenderTargetIdentifier rt, int mipLevel, CubemapFace cubemapFace, int depthSlice) ;

        /// <summary>Wraps [SetRenderTarget](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html) on a CommandBuffer.</summary>
        /// <param name="color">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="depth">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        public void SetRenderTarget(RenderTargetIdentifier color, RenderTargetIdentifier depth) ;

        /// <summary>Wraps [SetRenderTarget](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html) on a CommandBuffer.</summary>
        /// <param name="color">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="depth">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="mipLevel">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        public void SetRenderTarget(RenderTargetIdentifier color, RenderTargetIdentifier depth, int mipLevel) ;

        /// <summary>Wraps [SetRenderTarget](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html) on a CommandBuffer.</summary>
        /// <param name="color">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="depth">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="mipLevel">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="cubemapFace">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        public void SetRenderTarget(RenderTargetIdentifier color, RenderTargetIdentifier depth, int mipLevel, CubemapFace cubemapFace) ;

        /// <summary>Wraps [SetRenderTarget](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html) on a CommandBuffer.</summary>
        /// <param name="color">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="depth">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="mipLevel">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="cubemapFace">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="depthSlice">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        public void SetRenderTarget(RenderTargetIdentifier color, RenderTargetIdentifier depth, int mipLevel, CubemapFace cubemapFace, int depthSlice) ;

        /// <summary>Wraps [SetRenderTarget](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html) on a CommandBuffer.</summary>
        /// <param name="color">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="colorLoadAction">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="colorStoreAction">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="depth">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="depthLoadAction">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="depthStoreAction">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        public void SetRenderTarget(RenderTargetIdentifier color, RenderBufferLoadAction colorLoadAction, RenderBufferStoreAction colorStoreAction, RenderTargetIdentifier depth, RenderBufferLoadAction depthLoadAction, RenderBufferStoreAction depthStoreAction) ;

        /// <summary>Wraps [SetRenderTarget](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html) on a CommandBuffer.</summary>
        /// <param name="colors">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="depth">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        public void SetRenderTarget(RenderTargetIdentifier[] colors, RenderTargetIdentifier depth) ;

        /// <summary>Wraps [SetRenderTarget](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html) on a CommandBuffer.</summary>
        /// <param name="colors">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="depth">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="mipLevel">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="cubemapFace">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="depthSlice">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        public void SetRenderTarget(RenderTargetIdentifier[] colors, RenderTargetIdentifier depth, int mipLevel, CubemapFace cubemapFace, int depthSlice) ;

        /// <summary>Wraps [SetRenderTarget](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html) on a CommandBuffer.</summary>
        /// <param name="binding">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="mipLevel">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="cubemapFace">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        /// <param name="depthSlice">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        public void SetRenderTarget(RenderTargetBinding binding, int mipLevel, CubemapFace cubemapFace, int depthSlice) ;

        /// <summary>Wraps [SetRenderTarget](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html) on a CommandBuffer.</summary>
        /// <param name="binding">[See CommandBuffer documentation](https://docs.unity3d.com/ScriptReference/Rendering.CommandBuffer.SetRenderTarget.html)</param>
        public void SetRenderTarget(RenderTargetBinding binding) ;

    }
}
