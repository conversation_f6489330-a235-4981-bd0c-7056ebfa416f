Container
{
    border-top-width:1px;
    border-color:#000000;
    padding-top:10px;
    padding-bottom:10px;
}

Container.First
{
    border-top-width:0px;
}

Container.Selected > *
{
    margin-left:-5px;
    border-left-width:5px;
    border-color:#3d6091;
}

List > .Footer
{
    align-self:flex-end;
    flex-direction:row;
}

List > .Footer > *
{
    width:25px;
}



.unity-label
{
    min-width:90px;
}
