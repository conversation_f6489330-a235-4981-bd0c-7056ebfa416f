//
// This file was automatically generated. Please don't edit by hand. Execute Editor command [ Edit > Rendering > Generate Shader Includes ] instead
//

#ifndef PROBEGIBAKING_DILATE_CS_HLSL
#define PROBEGIBAKING_DILATE_CS_HLSL
// Generated from UnityEngine.Rendering.AdaptiveProbeVolumes+DilatedProbe
// PackingRules = Exact
struct DilatedProbe
{
    float3 L0;
    float3 L1_0;
    float3 L1_1;
    float3 L1_2;
    float3 L2_0;
    float3 L2_1;
    float3 L2_2;
    float3 L2_3;
    float3 L2_4;
    float4 SO_L0L1;
    float3 SO_Direction;
    float4 ProbeOcclusion;
};


#endif
