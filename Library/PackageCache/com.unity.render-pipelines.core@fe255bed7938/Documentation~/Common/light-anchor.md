# Light Anchor

![](Images/LightAnchor0.png)

You can use a Light Anchor to light a scene in Rendered Camera Space. To use a Light Anchor, you must connect it to a Light.

## Properties

| **Property**    | **Description**                                              |
| --------------- | ------------------------------------------------------------ |
| Orbit | Use the left icon to control the Orbit of the light. This tool becomes green when you move the icon. |
| Elevation | Use the middle icon to control the Elevation of the light. This tool becomes blue when you move the icon. |
| Roll | Use the right icon to control the Rollof the light. This tool becomes gray when you move the icon. This is especially useful if the light has an IES or a Cookie. |
| Distance | Controls the distance between the light and its anchor in world space. |
| Up Direction | Defines the space of the up direction of the anchor. When this value is set to Local, the Up Direction is relative to the camera. |
| Common | Assigns a preset to the light component based on the behaviour of studio lights. |
