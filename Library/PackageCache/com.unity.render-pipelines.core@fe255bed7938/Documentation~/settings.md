# Custom graphics settings

Add, store, and manage custom settings that control rendering features and behaviours in a Scriptable Render Pipeline. For example, quality or shader settings.

|Page|Description|
|-|-|
|[Adding properties in the menu](adding-properties.md)|Add properties in the **Core Render Pipeline** settings section.|
|[Add a settings group](add-custom-graphics-settings.md)|To add custom graphics settings to a Scriptable Render Pipeline, implement the `IRenderPipelineGraphicsSettings` interface. |
|[Add a setting](add-custom-graphics-setting.md)|Add a simple property or a reference property to a custom graphics settings group.|
|[Customize the UI of a setting](customize-ui-for-a-setting.md)|Customize how a setting displays in the graphics settings window, or add items to the **More** (⋮) menu of a settings group.|
|[Get custom graphics settings](get-custom-graphics-settings.md)|Get a custom graphics setting and read its value, or detect when a setting changes.|
|[Include or exclude a setting in your build](choose-whether-unity-includes-a-graphics-setting-in-your-build.md)|Choose whether Unity includes or strips a graphics setting in your build, and check if a build includes a setting.|

## Additional resources

- [Graphics settings](xref:class-GraphicsSettings) in the Unity manual
