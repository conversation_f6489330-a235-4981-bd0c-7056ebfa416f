{"name": "com.unity.inputsystem", "displayName": "Input System", "version": "1.14.0", "unity": "2021.3", "description": "A new input system which can be used as a more extensible and customizable alternative to Unity's classic input system in UnityEngine.Input.", "keywords": ["input", "events", "keyboard", "mouse", "gamepad", "touch", "vr", "xr"], "dependencies": {"com.unity.modules.uielements": "1.0.0"}, "_upm": {"changelog": "### Fixed\n- Fixed an issue where all action maps were enabled initially for project wide actions, which overrode the PlayerInput action map configuration. [ISXB-920](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-920)\n- Fixed an issue where ButtonStates are not fully updated when switching SingleUnifiedPointer. [ISXB-1356](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-1356)\n- Fixed errors when pasting composite parts into non-composites. [ISXB-757](https://issuetracker.unity3d.com/product/unity/issues/guid/ISXB-757)\n\n### Changed\n- Changed enum value `Key.IMESelected` to obsolete which was not a real key. Please use the ButtonControl `imeSelected`.\n\n### Added\n- Added support of F13-F24 keys. [UUM-44328](https://issuetracker.unity3d.com/product/unity/issues/guid/UUM-44328)"}, "upmCi": {"footprint": "502fede172521cf7f3e4563d5c450673c67aebd9"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.inputsystem@1.14/manual/index.html", "repository": {"url": "https://github.com/Unity-Technologies/InputSystem.git", "type": "git", "revision": "5ac6cb14e03a4e4e5faf7bd8fbd915105ae3ff55"}, "samples": [{"displayName": "Custom Binding Composite", "description": "Shows how to implement a custom composite binding.", "path": "Samples~/CustomComposite"}, {"displayName": "Custom Device", "description": "Shows how to implement a custom input device.", "path": "Samples~/CustomDevice"}, {"displayName": "Custom Device Usages", "description": "Shows how to tag devices with custom usage strings that can be used, for example, to distinguish multiple instances of the same type of device (e.g. 'Gamepad') based on how the device is used (e.g. 'Player1' vs 'Player2' or 'LeftHand' vs 'RightHand').", "path": "Samples~/CustomDeviceUsages"}, {"displayName": "Gamepad Mouse Cursor", "description": "An example that shows how to use the gamepad for driving a mouse cursor for use with UIs.", "path": "Samples~/GamepadMouseCursor"}, {"displayName": "In-Game Hints", "description": "Demonstrates how to create in-game hints in the UI which reflect current bindings and active control schemes.", "path": "Samples~/InGameHints"}, {"displayName": "InputDeviceTester", "description": "A scene containing UI to visualize the controls on various supported input devices.", "path": "Samples~/InputDeviceTester"}, {"displayName": "Input Recorder", "description": "Shows how to capture and replay input events. Also useful by itself to debug input event sequences.", "path": "Samples~/InputRecorder"}, {"displayName": "On-Screen Controls", "description": "Demonstrates a simple setup for an on-screen joystick.", "path": "Samples~/OnScreenControls"}, {"displayName": "Rebinding UI", "description": "An example UI component that demonstrates how to create UI for rebinding actions.", "path": "Samples~/RebindingUI"}, {"displayName": "Simple Demo", "description": "A walkthrough of a simple character controller that demonstrates several techniques for working with the input system. See the README.md file in the sample for details.", "path": "Samples~/SimpleDemo"}, {"displayName": "Simple Multiplayer", "description": "Demonstrates how to set up a simple local multiplayer scenario.", "path": "Samples~/SimpleMultiplayer"}, {"displayName": "Touch Samples", "description": "A series of sample scenes for using touch input with the Input System package. This sample is not actually part of the package, but needs to be downloaded.", "path": "Samples~/TouchSamples"}, {"displayName": "UI vs. Game Input", "description": "An example that shows how to deal with ambiguities that may arrise when overlaying interactive UI elements on top of a game scene.", "path": "Samples~/UIvsGameInput"}, {"displayName": "Unity Remote", "description": "An example with a simple scene for trying out the Unity Remote app.", "path": "Samples~/UnityRemote"}, {"displayName": "Visualizers", "description": "Several example visualizations of input controls/devices and input actions.", "path": "Samples~/Visualizers"}], "_fingerprint": "7fe8299111a78212d8968229ab41a82e4991ba25"}