This sample is both a demonstration for how to use [`InputEventTrace`](https://docs.unity3d.com/Packages/com.unity.inputsystem@latest/index.html?subfolder=/api/UnityEngine.InputSystem.LowLevel.InputEventTrace.html) as well as a useful tool by itself in the form of the [`InputRecorder`](./InputRecorder.cs) reusable `MonoBehaviour` component.

One possible way in which you can use this facility, for example, is to record input, save it to disk, and then replay the same input in automation (e.g. in tests or when recording short video snippets of preset gameplay sequences for manual proofing).
